# 第三章 电力线路脆弱性定义与构建方法

## 线路脆弱性的基本定义

### 概念定义

**线路脆弱性**：指电力线路系统在复杂载荷作用下发生性能退化、功能失效或结构损坏的内在倾向性。

**数学表达**：
$$
V_{line} = f(L_{mechanical}, L_{electrical}, L_{environmental}, L_{operational})
$$

其中：
- $V_{line}$：线路脆弱性总值
- $L_{mechanical}$：机械载荷响应属性
- $L_{electrical}$：电气载荷响应属性
- $L_{environmental}$：环境载荷响应属性
- $L_{operational}$：运行载荷响应属性

### 线路脆弱性特征

**线路系统的独特性**：
1. **线性延伸特性**：跨越不同地理和气候区域
2. **多点脆弱性**：任一点故障影响整条线路
3. **载荷累积效应**：多种载荷同时作用产生叠加
4. **环境梯度敏感**：沿线环境变化影响显著

**脆弱性表现形式**：
- **机械失效**：导线断股、杆塔倒塌、绝缘子破损
- **电气失效**：绝缘闪络、接地故障、相间短路
- **功能退化**：输送能力下降、可靠性降低
- **维护困难**：巡检不便、抢修复杂

## 线路载荷响应属性体系

基于线路系统特点，建立六大载荷响应属性，全面覆盖线路脆弱性的各个方面：

**机械载荷维度**：风载荷、冰载荷
**电气载荷维度**：雷电载荷、污秽载荷  
**环境载荷维度**：温度载荷、腐蚀载荷

### 风载荷响应属性 ($L_{wind}$) - 机械载荷

**定义**：线路对风载荷的机械响应程度

**风载荷计算模型**：
$$
F_{wind} = \frac{1}{2} \rho v^2 C_d A
$$

其中：
- $\rho$：空气密度 (kg/m³)
- $v$：风速 (m/s)
- $C_d$：阻力系数
- $A$：受风面积 (m²)

**风载荷暴露函数**：
$$
E_{wind}(F) = \begin{cases}
0 & F < F_{design} \\
\frac{F - F_{design}}{F_{ultimate} - F_{design}} & F_{design} \leq F \leq F_{ultimate} \\
2 & F > F_{ultimate}
\end{cases}
$$

**风载荷敏感性系数**：
$$
S_{wind} = \frac{档距系数 \times 高度系数 \times 地形系数}{结构强度系数}
$$

**风载荷响应属性**：
$$
L_{wind} = E_{wind}(F) \times S_{wind}
$$

### 冰载荷响应属性 ($L_{ice}$) - 机械载荷

**定义**：线路对覆冰载荷的机械响应程度

**冰载荷计算模型**：
$$
F_{ice} = \gamma_{ice} \times V_{ice} \times g
$$

其中：
- $\gamma_{ice}$：冰的容重 (kN/m³)
- $V_{ice}$：覆冰体积 (m³)
- $g$：重力加速度 (m/s²)

**冰载荷暴露函数**：
$$
E_{ice}(t, \rho) = \frac{t}{10} \times \frac{\rho}{0.9} \times 覆冰厚度因子
$$

其中：
- $t$：覆冰厚度 (mm)
- $\rho$：冰密度 (g/cm³)

**冰载荷敏感性系数**：
$$
S_{ice} = 导线系数 \times 绝缘子系数 \times 杆塔系数
$$

**冰载荷响应属性**：
$$
L_{ice} = E_{ice}(t, \rho) \times S_{ice}
$$

### 雷电载荷响应属性 ($L_{lightning}$) - 电气载荷

**定义**：线路对雷电冲击的电气响应程度

**雷电载荷计算模型**：
$$
U_{lightning} = I \times Z_{surge}
$$

其中：
- $I$：雷电流幅值 (kA)
- $Z_{surge}$：线路波阻抗 (Ω)

**雷电载荷暴露函数**：
$$
E_{lightning}(I, N) = \frac{I}{50} \times \sqrt{\frac{N}{30}}
$$

其中：
- $I$：雷电流幅值 (kA)
- $N$：年雷暴日数

**雷电载荷敏感性系数**：
$$
S_{lightning} = 绝缘水平系数 \times 接地电阻系数 \times 避雷线保护系数
$$

**雷电载荷响应属性**：
$$
L_{lightning} = E_{lightning}(I, N) \times S_{lightning}
$$

### 污秽载荷响应属性 ($L_{contamination}$) - 电气载荷

**定义**：线路对环境污秽的电气响应程度

**污秽载荷计算模型**：
$$
U_{flashover} = A \times (ESDD)^{-n} \times L_{creepage}
$$

其中：
- $ESDD$：等值盐密 (mg/cm²)
- $L_{creepage}$：爬距 (mm)
- $A, n$：常数

**污秽载荷暴露函数**：
$$
E_{contamination}(ESDD, NSDD) = \frac{ESDD}{0.1} \times \sqrt{\frac{NSDD}{1.0}}
$$

其中：
- $ESDD$：等值盐密 (mg/cm²)
- $NSDD$：等值灰密 (mg/cm²)

**污秽载荷敏感性系数**：
$$
S_{contamination} = \frac{绝缘子类型系数}{自清洁能力系数}
$$

**污秽载荷响应属性**：
$$
L_{contamination} = E_{contamination}(ESDD, NSDD) \times S_{contamination}
$$

### 温度载荷响应属性 ($L_{temperature}$) - 环境载荷

**定义**：线路对温度变化的热机械响应程度

**温度载荷计算模型**：
$$
\sigma_{thermal} = E \times \alpha \times \Delta T
$$

其中：
- $E$：弹性模量 (GPa)
- $\alpha$：热膨胀系数 (1/°C)
- $\Delta T$：温度变化 (°C)

**温度载荷暴露函数**：
$$
E_{temperature}(\Delta T, t) = \frac{|\Delta T|}{50} \times \min(2.0, \frac{t}{24})
$$

其中：
- $\Delta T$：温度变化幅度 (°C)
- $t$：持续时间 (小时)

**温度载荷敏感性系数**：
$$
S_{temperature} = 材料系数 \times 约束系数 \times 老化系数
$$

**温度载荷响应属性**：
$$
L_{temperature} = E_{temperature}(\Delta T, t) \times S_{temperature}
$$

### 腐蚀载荷响应属性 ($L_{corrosion}$) - 环境载荷

**定义**：线路对环境腐蚀的材料退化响应程度

**腐蚀载荷计算模型**：
$$
R_{corrosion} = k \times C^n \times t^m
$$

其中：
- $k$：腐蚀常数
- $C$：腐蚀介质浓度
- $t$：时间
- $n, m$：指数

**腐蚀载荷暴露函数**：
$$
E_{corrosion}(pH, Cl^-, SO_2) = \frac{|7-pH|}{3} + \frac{Cl^-}{100} + \frac{SO_2}{0.1}
$$

其中：
- $pH$：酸碱度
- $Cl^-$：氯离子浓度 (mg/L)
- $SO_2$：二氧化硫浓度 (mg/m³)

**腐蚀载荷敏感性系数**：
$$
S_{corrosion} = 材料敏感系数 \times 防护系数^{-1}
$$

**腐蚀载荷响应属性**：
$$
L_{corrosion} = E_{corrosion}(pH, Cl^-, SO_2) \times S_{corrosion}
$$

## 线路脆弱性综合计算模型

### 载荷耦合效应

考虑到不同载荷间的相互作用，引入耦合效应：

$$
C_{coupling} = \sqrt{L_{wind} \times L_{ice}} + 0.5 \times L_{lightning} \times L_{contamination}
$$

### 综合脆弱性计算

$$
V_{line} = \sum_{i=1}^{6} w_i \times L_i^* + w_c \times C_{coupling}^*
$$

其中权重分配：
- $w_1 = 0.25$（风载荷权重）
- $w_2 = 0.20$（冰载荷权重）
- $w_3 = 0.20$（雷电载荷权重）
- $w_4 = 0.15$（污秽载荷权重）
- $w_5 = 0.10$（温度载荷权重）
- $w_6 = 0.05$（腐蚀载荷权重）
- $w_c = 0.05$（耦合效应权重）

归一化处理：$L_i^* = \frac{L_i}{L_{i,max}}$

## 小结

本章建立了基于载荷响应的线路脆弱性评估方法：

1. **载荷导向**：从机械、电气、环境载荷角度分析
2. **物理机理**：基于载荷计算的物理模型
3. **耦合考虑**：考虑不同载荷间的相互作用
4. **工程实用**：权重设计基于工程经验

该方法为线路设计和运维提供了科学依据。
